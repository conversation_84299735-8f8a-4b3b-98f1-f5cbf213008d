import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const DATA_DIR = path.join(__dirname, 'data');
const LATEST_DATA_PATH = path.join(DATA_DIR, 'latest.json');
const RESET_CHANGELOG_PATH = path.join(__dirname, 'reset-changelog.md');

async function generateResetChangelog() {
  try {
    const latestData = await fs.readJson(LATEST_DATA_PATH);
    const locations = latestData.locations;

    // Group locations by lastResetDate
    const groupedByDate = {};
    for (const location of locations) {
      if (!location.lastResetDate) continue;
      const date = location.lastResetDate;
      if (!groupedByDate[date]) {
        groupedByDate[date] = [];
      }
      groupedByDate[date].push(location);
    }

    // Sort dates in descending order
    const sortedDates = Object.keys(groupedByDate).sort(
      (a, b) => new Date(b) - new Date(a),
    );

    let markdown = '# CDC Vouchers Reset Log\n\n';

    for (const date of sortedDates) {
      markdown += `<details${
        date === sortedDates[0] ? ' open' : ''
      }><summary>\n\n## ${date} (${
        groupedByDate[date].length
      })\n\n</summary>\n\n`;
      markdown += '| Name | Address | Coordinates |\n|---|---|---|\n';

      for (const location of groupedByDate[date]) {
        const coords =
          location.LAT && location.LON
            ? `<span title="${location.LAT},${
                location.LON
              }">${location.LAT.toFixed(5)}, ${location.LON.toFixed(5)}</span>`
            : 'N/A';
        markdown += `| ${location.name} | ${location.address} | ${coords} |\n`;
      }

      markdown += '\n</details>\n\n';
    }

    await fs.writeFile(RESET_CHANGELOG_PATH, markdown);
    console.log('Reset changelog generated successfully');
  } catch (error) {
    console.error('Error generating reset changelog:', error);
    process.exit(1);
  }
}

generateResetChangelog();
